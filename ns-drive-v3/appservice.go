package main

import (
	"context"
	"ns-drive/backend"

	"github.com/wailsapp/wails/v3/pkg/application"
)

// AppService wraps the backend App to implement the Wails v3 service interface
type AppService struct {
	app *backend.App
}

// NewAppService creates a new AppService
func NewAppService() *AppService {
	return &AppService{
		app: backend.NewApp(),
	}
}

// ServiceStartup is called when the service starts
func (s *AppService) ServiceStartup(ctx context.Context, options application.ServiceOptions) error {
	return s.app.ServiceStartup(ctx, options)
}

// SetApp sets the application reference for events
func (s *AppService) SetApp(app *application.App) {
	s.app.SetApp(app)
}

// Expose all the backend methods through the service

func (s *AppService) Sync(task string, profile backend.Profile) int {
	return s.app.Sync(task, profile)
}

func (s *AppService) SyncWithTab(task string, profile backend.Profile, tabId string) int {
	return s.app.SyncWithTab(task, profile, tabId)
}

func (s *AppService) GetProfiles() []backend.Profile {
	return s.app.GetProfiles()
}

func (s *AppService) GetProfile(id string) backend.Profile {
	return s.app.GetProfile(id)
}

func (s *AppService) SaveProfile(profile backend.Profile) error {
	return s.app.SaveProfile(profile)
}

func (s *AppService) DeleteProfile(id string) error {
	return s.app.DeleteProfile(id)
}

func (s *AppService) GetRemotes() []backend.Remote {
	return s.app.GetRemotes()
}

func (s *AppService) GetRemote(name string) backend.Remote {
	return s.app.GetRemote(name)
}

func (s *AppService) CreateRemote(remote backend.Remote) error {
	return s.app.CreateRemote(remote)
}

func (s *AppService) UpdateRemote(remote backend.Remote) error {
	return s.app.UpdateRemote(remote)
}

func (s *AppService) DeleteRemote(name string) error {
	return s.app.DeleteRemote(name)
}

func (s *AppService) TestRemote(remote backend.Remote) error {
	return s.app.TestRemote(remote)
}

func (s *AppService) GetConfigInfo() backend.ConfigInfo {
	return s.app.GetConfigInfo()
}

func (s *AppService) GetWorkingDir() string {
	return s.app.GetWorkingDir()
}

func (s *AppService) StopSync(tabId string) error {
	return s.app.StopSync(tabId)
}

func (s *AppService) GetSyncStatus(tabId string) backend.SyncStatus {
	return s.app.GetSyncStatus(tabId)
}

func (s *AppService) ClearOutput(tabId string) error {
	return s.app.ClearOutput(tabId)
}
