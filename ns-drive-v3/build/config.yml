# This file contains the configuration for this project.
# When you update `info` or `fileAssociations`, run `wails3 task common:update:build-assets` to update the assets.
# Note that this will overwrite any changes you have made to the assets.
version: "3"

# This information is used to generate the build assets.
info:
  companyName: "ns-drive" # The name of the company
  productName: "ns-drive" # The name of the application
  productIdentifier: "com.nsdrive.app" # The unique product identifier
  description: "A desktop application for rclone file synchronization" # The application description
  copyright: "(c) 2024, ns-drive" # Copyright text
  comments: "Desktop application for rclone file synchronization" # Comments
  version: "v1.0.0" # The application version

# Dev mode configuration
dev_mode:
  root_path: .
  log_level: warn
  debounce: 1000
  ignore:
    dir:
      - .git
      - node_modules
      - frontend
      - bin
    file:
      - .DS_Store
      - .gitignore
      - .gitkeep
    watched_extension:
      - "*.go"
    git_ignore: true
  executes:
    - cmd: wails3 task common:install:frontend:deps
      type: once
    - cmd: wails3 task common:dev:frontend
      type: background
    - cmd: go mod tidy
      type: blocking
    - cmd: wails3 task build
      type: blocking
    - cmd: wails3 task run
      type: primary

# File Associations
# More information at: https://v3alpha.wails.io/noit/done/yet
fileAssociations:
#  - ext: wails
#    name: Wails
#    description: Wails Application File
#    iconName: wailsFileIcon
#    role: Editor
#  - ext: jpg
#    name: JPEG
#    description: Image File
#    iconName: jpegFileIcon
#    role: Editor

# Other data
other:
  - name: My Other Data
