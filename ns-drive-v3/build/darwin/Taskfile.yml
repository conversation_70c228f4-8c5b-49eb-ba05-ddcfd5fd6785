version: '3'

includes:
  common: ../Taskfile.yml

tasks:
  build:
    summary: Creates a production build of the application
    deps:
      - task: common:go:mod:tidy
      - task: common:build:frontend
      - task: common:generate:icons
    cmds:
      - go build {{.BUILD_FLAGS}} -o {{.OUTPUT}}
    vars:
      BUILD_FLAGS: '{{if eq .PRODUCTION "true"}}-tags production -trimpath -buildvcs=false -ldflags="-w -s"{{else}}-buildvcs=false -gcflags=all="-l"{{end}}'
      DEFAULT_OUTPUT: '{{.BIN_DIR}}/{{.APP_NAME}}'
      OUTPUT: '{{ .OUTPUT | default .DEFAULT_OUTPUT }}'
    env:
      GOOS: darwin
      CGO_ENABLED: 1
      GOARCH: '{{.ARCH | default ARCH}}'
      CGO_CFLAGS: "-mmacosx-version-min=10.15"
      CGO_LDFLAGS: "-mmacosx-version-min=10.15"
      MACOSX_DEPLOYMENT_TARGET: "10.15"
      PRODUCTION: '{{.PRODUCTION | default "false"}}'

  build:universal:
    summary: Builds darwin universal binary (arm64 + amd64)
    deps:
      - task: build
        vars:
          ARCH: amd64
          OUTPUT: "{{.BIN_DIR}}/{{.APP_NAME}}-amd64"
      - task: build
        vars:
          ARCH: arm64
          OUTPUT: "{{.BIN_DIR}}/{{.APP_NAME}}-arm64"
    cmds:
      - lipo -create -output "{{.BIN_DIR}}/{{.APP_NAME}}" "{{.BIN_DIR}}/{{.APP_NAME}}-amd64" "{{.BIN_DIR}}/{{.APP_NAME}}-arm64"
      - rm "{{.BIN_DIR}}/{{.APP_NAME}}-amd64" "{{.BIN_DIR}}/{{.APP_NAME}}-arm64"

  package:
    summary: Packages a production build of the application into a `.app` bundle
    deps:
      - task: build
        vars:
          PRODUCTION: "true"
    cmds:
      - task: create:app:bundle

  package:universal:
    summary: Packages darwin universal binary (arm64 + amd64)
    deps:
      - task: build:universal
    cmds:
      - task: create:app:bundle


  create:app:bundle:
    summary: Creates an `.app` bundle
    cmds:
      - mkdir -p {{.BIN_DIR}}/{{.APP_NAME}}.app/Contents/{MacOS,Resources}
      - cp build/darwin/icons.icns {{.BIN_DIR}}/{{.APP_NAME}}.app/Contents/Resources
      - cp {{.BIN_DIR}}/{{.APP_NAME}} {{.BIN_DIR}}/{{.APP_NAME}}.app/Contents/MacOS
      - cp build/darwin/Info.plist {{.BIN_DIR}}/{{.APP_NAME}}.app/Contents

  run:
    cmds:
      - '{{.BIN_DIR}}/{{.APP_NAME}}'
