version: "3"

tasks:
  go:mod:tidy:
    summary: Runs `go mod tidy`
    internal: true
    cmds:
      - go mod tidy

  install:frontend:deps:
    summary: Install frontend dependencies
    dir: frontend
    sources:
      - package.json
      - yarn.lock
    generates:
      - node_modules/*
    preconditions:
      - sh: yarn --version
        msg: "Looks like yarn isn't installed. Please install yarn: https://yarnpkg.com/getting-started/install"
    cmds:
      - yarn install

  build:frontend:
    summary: Build the frontend project
    dir: frontend
    sources:
      - "**/*"
    generates:
      - dist/browser/*
    deps:
      - task: install:frontend:deps
      - task: generate:bindings
    cmds:
      - yarn build
    env:
      PRODUCTION: '{{.PRODUCTION | default "true"}}'

  generate:bindings:
    summary: Generates bindings for the frontend
    deps:
      - task: go:mod:tidy
    sources:
      - "**/*.go"
      - go.mod
      - go.sum
    generates:
      - "frontend/bindings/**/*"
    cmds:
      - wails3 generate bindings -f '{{.BUILD_FLAGS}}' -clean=true  -ts

  generate:icons:
    summary: Generates Windows `.ico` and Mac `.icns` files from an image
    dir: build
    sources:
      - "appicon.png"
    generates:
      - "icons.icns"
      - "icons.ico"
    cmds:
      - wails3 generate icons -input appicon.png -macfilename darwin/icons.icns -windowsfilename windows/icons.ico

  dev:frontend:
    summary: Runs the frontend in development mode
    dir: frontend
    deps:
      - task: install:frontend:deps
    cmds:
      - yarn start --port {{.VITE_PORT}}

  update:build-assets:
    summary: Updates the build assets
    dir: build
    cmds:
      - wails3 update build-assets -name "{{.APP_NAME}}" -binaryname "{{.APP_NAME}}" -config config.yml -dir .
