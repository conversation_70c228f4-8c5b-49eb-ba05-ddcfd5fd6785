version: "3"

vars:
  APP_NAME: ns-drive
  BIN_DIR: bin
  VITE_PORT: "{{.WAILS_VITE_PORT | default 9245}}"

tasks:
  # Desktop - Wails v3 tasks
  build:
    summary: Builds the application
    cmds:
      - task: install:frontend:deps
      - task: build:frontend
      - task: generate:bindings
      - go build -ldflags="-s -w" -o {{.BIN_DIR}}/{{.APP_NAME}}
    dir: desktop
    env:
      CGO_CFLAGS: "-mmacosx-version-min=14.0"
      CGO_LDFLAGS: "-mmacosx-version-min=14.0"
      MACOSX_DEPLOYMENT_TARGET: "14.0"

  package:
    summary: Packages a production build of the application
    cmds:
      - task: build
      - $(go env GOPATH)/bin/wails3 task package
    dir: desktop

  run:
    summary: Runs the application
    cmds:
      - ./{{.BIN_DIR}}/{{.APP_NAME}}
    dir: desktop
    deps:
      - build

  dev:
    summary: Starts both frontend and backend dev servers (experimental)
    cmds:
      - echo "Checking for processes on port {{.VITE_PORT}}..."
      - lsof -ti:{{.VITE_PORT}} | xargs -r kill -9 || true
      - echo "Starting Angular dev server..."
      - cd frontend && yarn start --port {{.VITE_PORT}} &
      - sleep 10
      - echo "Starting Wails v3 dev server..."
      - cd ..
      - $(go env GOPATH)/bin/wails3 dev -config ./build/config.yml -port {{.VITE_PORT}}
    dir: desktop

  # Frontend tasks
  install:frontend:deps:
    summary: Install frontend dependencies
    dir: desktop/frontend
    cmds:
      - yarn install

  build:frontend:
    summary: Build frontend for production
    dir: desktop/frontend
    cmds:
      - yarn build
    sources:
      - src/**/*
      - package.json
      - angular.json
      - tsconfig.json
    generates:
      - dist/browser/**/*

  # Development utilities
  generate:bindings:
    summary: Generate TypeScript bindings from Go code
    dir: desktop
    cmds:
      - $(go env GOPATH)/bin/wails3 generate bindings

  clean:
    summary: Clean build artifacts
    dir: desktop
    cmds:
      - rm -rf {{.BIN_DIR}}/
      - rm -rf frontend/dist/browser

  fmt:
    summary: Format Go code
    dir: desktop
    cmds:
      - go fmt ./...

  test:
    summary: Run tests
    dir: desktop
    cmds:
      - go test ./...
